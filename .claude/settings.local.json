{"permissions": {"allow": ["WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(claude mcp:*)", "mcp__taskmaster-ai__models", "mcp__brave-search__brave_web_search", "mcp__context7-mcp__resolve-library-id", "mcp__supabase-mcp__list_organizations", "mcp__browser-tools-mcp__takeScreenshot", "mcp__sentry__whoami", "mcp__playwright__playwright_navigate", "mcp__playwright__playwright_close", "mcp__sequential-thinking__sequentialthinking", "Bash(find:*)", "Bash(npm audit:*)", "Bash(ls:*)", "Bash(git checkout:*)", "Bash(npm install:*)", "Bash(npx npm-check-updates:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm test)", "Bash(npm run clean:build:*)", "mcp__playwright__playwright_screenshot", "mcp__playwright__playwright_console_logs", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "mcp__playwright__playwright_get_visible_html", "Bash(npm run clean:*)", "Bash(npm run dev:*)", "Bash(npm run build:*)", "Bash(npm run lint)", "mcp__context7-mcp__get-library-docs", "<PERSON><PERSON>(curl:*)", "Bash(npx tailwindcss:*)", "Bash(./node_modules/.bin/tailwindcss:*)", "Bash(node:*)", "WebFetch(domain:tailwindcss.com)", "mcp__playwright__playwright_evaluate", "Bash(git add:*)", "Bash(grep:*)", "Bash(npm test:*)", "Bash(rm:*)", "Bash(npm cache clean:*)", "Bash(npx tsc:*)", "mcp__playwright__playwright_fill", "mcp__playwright__playwright_get_visible_text", "mcp__playwright__playwright_click", "mcp__playwright__playwright_press_key", "Bash(npm ls:*)", "Bash(git commit:*)", "Bash(git fetch:*)", "Bash(git merge:*)", "Bash(npm run test:*)", "<PERSON><PERSON>(npx playwright test:*)", "<PERSON><PERSON>(npx playwright:*)", "Bash(echo)", "<PERSON><PERSON>(cat:*)"], "deny": []}}