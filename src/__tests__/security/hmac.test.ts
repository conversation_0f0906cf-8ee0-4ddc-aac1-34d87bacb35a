// src/__tests__/security/hmac.test.ts
// Comprehensive test suite for HMAC authentication helper

import {
  generateHMACSignature,
  verifyHMACSignature,
  extractHMACFromRequest,
  verifyRequestHMAC,
  createHMACHeaders,
  isAuthenticationEnabled,
  isHMACEnabled,
  shouldEnforceAuthentication,
  clearReplayCache,
  getReplayCacheSize
} from '@/lib/security/hmac'
import { NextRequest } from 'next/server'

// Test configuration
const TEST_SECRET = 'test-secret-minimum-32-characters-long'
const TEST_PARTNER_ID = 'test-partner'

// Setup test environment
beforeAll(() => {
  process.env.PARTNER_SECRET_TEST_PARTNER = TEST_SECRET
  process.env.PARTNER_SECRET_DEFAULT = TEST_SECRET
  process.env.HMAC_TIMESTAMP_WINDOW = '300'
  process.env.ENABLE_SEARCH_AUTH = 'true'
  process.env.ENABLE_HMAC_AUTH = 'true'
})

afterAll(() => {
  delete process.env.PARTNER_SECRET_TEST_PARTNER
  delete process.env.PARTNER_SECRET_DEFAULT
  delete process.env.HMAC_TIMESTAMP_WINDOW
  delete process.env.ENABLE_SEARCH_AUTH
  delete process.env.ENABLE_HMAC_AUTH
})

// Clear replay cache before each test to ensure clean state
beforeEach(() => {
  clearReplayCache()
})

describe('HMAC Helper Functions', () => {
  describe('generateHMACSignature', () => {
    it('generates correct signature for GET request', () => {
      const signature = generateHMACSignature(
        'GET',
        '/api/search',
        1705123456,
        '',
        TEST_SECRET
      )
      expect(signature).toMatch(/^[a-f0-9]{64}$/)
      expect(signature.length).toBe(64)
    })
    
    it('generates correct signature for POST request with body', () => {
      const body = JSON.stringify({ query: 'laptop' })
      const signature = generateHMACSignature(
        'POST',
        '/api/search',
        1705123456,
        body,
        TEST_SECRET
      )
      expect(signature).toMatch(/^[a-f0-9]{64}$/)
    })
    
    it('generates different signatures for different methods', () => {
      const getSignature = generateHMACSignature('GET', '/api/search', 1705123456, '', TEST_SECRET)
      const postSignature = generateHMACSignature('POST', '/api/search', 1705123456, '', TEST_SECRET)
      expect(getSignature).not.toBe(postSignature)
    })
    
    it('generates different signatures for different paths', () => {
      const searchSignature = generateHMACSignature('GET', '/api/search', 1705123456, '', TEST_SECRET)
      const suggestionsSignature = generateHMACSignature('GET', '/api/search/suggestions', 1705123456, '', TEST_SECRET)
      expect(searchSignature).not.toBe(suggestionsSignature)
    })
    
    it('generates different signatures for different timestamps', () => {
      const signature1 = generateHMACSignature('GET', '/api/search', 1705123456, '', TEST_SECRET)
      const signature2 = generateHMACSignature('GET', '/api/search', 1705123457, '', TEST_SECRET)
      expect(signature1).not.toBe(signature2)
    })

    it('generates different signatures for different bodies', () => {
      const signature1 = generateHMACSignature('POST', '/api/search', 1705123456, '', TEST_SECRET)
      const signature2 = generateHMACSignature('POST', '/api/search', 1705123456, '{"query":"test"}', TEST_SECRET)
      expect(signature1).not.toBe(signature2)
    })
  })
  
  describe('verifyHMACSignature', () => {
    it('verifies valid signature', () => {
      const signature = generateHMACSignature('GET', '/api/search', 1705123456, '', TEST_SECRET)
      const isValid = verifyHMACSignature(signature, 'GET', '/api/search', 1705123456, '', TEST_SECRET)
      expect(isValid).toBe(true)
    })
    
    it('rejects invalid signature', () => {
      const isValid = verifyHMACSignature('invalid-signature', 'GET', '/api/search', 1705123456, '', TEST_SECRET)
      expect(isValid).toBe(false)
    })
    
    it('rejects signature with wrong secret', () => {
      const signature = generateHMACSignature('GET', '/api/search', 1705123456, '', 'secret1')
      const isValid = verifyHMACSignature(signature, 'GET', '/api/search', 1705123456, '', 'secret2')
      expect(isValid).toBe(false)
    })

    it('rejects signature with wrong method', () => {
      const signature = generateHMACSignature('GET', '/api/search', 1705123456, '', TEST_SECRET)
      const isValid = verifyHMACSignature(signature, 'POST', '/api/search', 1705123456, '', TEST_SECRET)
      expect(isValid).toBe(false)
    })

    it('rejects signature with wrong path', () => {
      const signature = generateHMACSignature('GET', '/api/search', 1705123456, '', TEST_SECRET)
      const isValid = verifyHMACSignature(signature, 'GET', '/api/different', 1705123456, '', TEST_SECRET)
      expect(isValid).toBe(false)
    })
  })
  
  describe('extractHMACFromRequest', () => {
    it('extracts valid HMAC data from request', () => {
      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': 'sha256=abc123',
          'x-timestamp': '1705123456',
          'x-partner-id': TEST_PARTNER_ID
        }
      })
      
      const hmacData = extractHMACFromRequest(request)
      expect(hmacData).toEqual({
        signature: 'abc123',
        timestamp: 1705123456,
        partnerId: TEST_PARTNER_ID
      })
    })
    
    it('returns null for missing headers', () => {
      const request = new NextRequest('http://localhost/api/search')
      const hmacData = extractHMACFromRequest(request)
      expect(hmacData).toBeNull()
    })
    
    it('handles sha256= prefix in signature', () => {
      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': 'sha256=abc123',
          'x-timestamp': '1705123456',
          'x-partner-id': TEST_PARTNER_ID
        }
      })
      
      const hmacData = extractHMACFromRequest(request)
      expect(hmacData?.signature).toBe('abc123')
    })

    it('handles signature without sha256= prefix', () => {
      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': 'abc123',
          'x-timestamp': '1705123456',
          'x-partner-id': TEST_PARTNER_ID
        }
      })
      
      const hmacData = extractHMACFromRequest(request)
      expect(hmacData?.signature).toBe('abc123')
    })

    it('returns null for invalid timestamp', () => {
      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': 'abc123',
          'x-timestamp': 'invalid',
          'x-partner-id': TEST_PARTNER_ID
        }
      })
      
      const hmacData = extractHMACFromRequest(request)
      expect(hmacData).toBeNull()
    })
  })
  
  describe('verifyRequestHMAC', () => {
    it('verifies valid HMAC request', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
      
      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': `sha256=${signature}`,
          'x-timestamp': timestamp.toString(),
          'x-partner-id': TEST_PARTNER_ID
        }
      })
      
      const payload = await verifyRequestHMAC(request)
      expect(payload).toEqual({
        partnerId: TEST_PARTNER_ID,
        timestamp,
        method: 'GET',
        path: '/api/search',
        isValid: true
      })
    })
    
    it('rejects expired timestamp', async () => {
      const expiredTimestamp = Math.floor(Date.now() / 1000) - 400 // 400 seconds ago
      const signature = generateHMACSignature('GET', '/api/search', expiredTimestamp, '', TEST_SECRET)
      
      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': `sha256=${signature}`,
          'x-timestamp': expiredTimestamp.toString(),
          'x-partner-id': TEST_PARTNER_ID
        }
      })
      
      const payload = await verifyRequestHMAC(request)
      expect(payload).toBeNull()
    })
    
    it('rejects unknown partner', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'unknown-secret')
      
      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': `sha256=${signature}`,
          'x-timestamp': timestamp.toString(),
          'x-partner-id': 'unknown-partner'
        }
      })
      
      const payload = await verifyRequestHMAC(request)
      expect(payload).toBeNull()
    })

    it('rejects future timestamp', async () => {
      const futureTimestamp = Math.floor(Date.now() / 1000) + 400 // 400 seconds in future
      const signature = generateHMACSignature('GET', '/api/search', futureTimestamp, '', TEST_SECRET)
      
      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': `sha256=${signature}`,
          'x-timestamp': futureTimestamp.toString(),
          'x-partner-id': TEST_PARTNER_ID
        }
      })
      
      const payload = await verifyRequestHMAC(request)
      expect(payload).toBeNull()
    })

    it('detects replay attacks', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)

      const request1 = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': `sha256=${signature}`,
          'x-timestamp': timestamp.toString(),
          'x-partner-id': TEST_PARTNER_ID
        }
      })

      const request2 = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': `sha256=${signature}`,
          'x-timestamp': timestamp.toString(),
          'x-partner-id': TEST_PARTNER_ID
        }
      })

      // First request should succeed
      const payload1 = await verifyRequestHMAC(request1)
      expect(payload1).not.toBeNull()

      // Second identical request should be rejected as replay
      const payload2 = await verifyRequestHMAC(request2)
      expect(payload2).toBeNull()
    })
  })
  
  describe('createHMACHeaders', () => {
    it('creates valid HMAC headers', () => {
      const headers = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
      
      expect(headers).toHaveProperty('X-Signature')
      expect(headers).toHaveProperty('X-Timestamp')
      expect(headers).toHaveProperty('X-Partner-ID')
      expect(headers['X-Partner-ID']).toBe(TEST_PARTNER_ID)
      expect(headers['X-Signature']).toMatch(/^sha256=[a-f0-9]{64}$/)
      expect(headers['X-Timestamp']).toMatch(/^\d+$/)
    })

    it('throws error for unknown partner', () => {
      // Temporarily remove default secret to test error case
      const originalDefault = process.env.PARTNER_SECRET_DEFAULT
      delete process.env.PARTNER_SECRET_DEFAULT

      expect(() => {
        createHMACHeaders('GET', '/api/search', 'unknown-partner', '')
      }).toThrow('No secret available for partner: unknown-partner')

      // Restore default secret
      if (originalDefault) {
        process.env.PARTNER_SECRET_DEFAULT = originalDefault
      }
    })
  })

  describe('Feature Flags', () => {
    it('isAuthenticationEnabled returns correct value', () => {
      expect(isAuthenticationEnabled()).toBe(true)

      process.env.ENABLE_SEARCH_AUTH = 'false'
      expect(isAuthenticationEnabled()).toBe(false)

      process.env.ENABLE_SEARCH_AUTH = 'true'
    })

    it('isHMACEnabled returns correct value', () => {
      expect(isHMACEnabled()).toBe(true)

      process.env.ENABLE_HMAC_AUTH = 'false'
      expect(isHMACEnabled()).toBe(false)

      process.env.ENABLE_HMAC_AUTH = 'true'
    })

    it('shouldEnforceAuthentication respects feature flag', () => {
      expect(shouldEnforceAuthentication('/api/search')).toBe(true)

      process.env.ENABLE_SEARCH_AUTH = 'false'
      expect(shouldEnforceAuthentication('/api/search')).toBe(false)

      process.env.ENABLE_SEARCH_AUTH = 'true'
    })

    it('shouldEnforceAuthentication allows public access to suggestions', () => {
      // Suggestions endpoint should be public regardless of auth settings
      expect(shouldEnforceAuthentication('/api/search/suggestions')).toBe(false)
      
      // Even when auth is enabled, suggestions should remain public
      process.env.ENABLE_SEARCH_AUTH = 'true'
      expect(shouldEnforceAuthentication('/api/search/suggestions')).toBe(false)
      
      // Main search endpoint should still require auth
      expect(shouldEnforceAuthentication('/api/search')).toBe(true)
    })
  })

  describe('Feature Flag Permutations', () => {
    afterEach(() => {
      // Restore default values
      process.env.ENABLE_SEARCH_AUTH = 'true'
      process.env.ENABLE_HMAC_AUTH = 'true'
    })

    it('handles ENABLE_SEARCH_AUTH=false (authentication disabled)', async () => {
      process.env.ENABLE_SEARCH_AUTH = 'false'

      // Authentication should be disabled regardless of HMAC flag
      expect(shouldEnforceAuthentication('/api/search')).toBe(false)
      expect(isAuthenticationEnabled()).toBe(false)

      // HMAC verification should still work for testing purposes
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)

      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': `sha256=${signature}`,
          'x-timestamp': timestamp.toString(),
          'x-partner-id': TEST_PARTNER_ID
        }
      })

      const payload = await verifyRequestHMAC(request)
      expect(payload).not.toBeNull()
    })

    it('handles ENABLE_SEARCH_AUTH=true, ENABLE_HMAC_AUTH=false (JWT only)', () => {
      process.env.ENABLE_SEARCH_AUTH = 'true'
      process.env.ENABLE_HMAC_AUTH = 'false'

      expect(shouldEnforceAuthentication('/api/search')).toBe(true)
      expect(isAuthenticationEnabled()).toBe(true)
      expect(isHMACEnabled()).toBe(false)
    })

    it('handles ENABLE_SEARCH_AUTH=true, ENABLE_HMAC_AUTH=true (dual auth)', () => {
      process.env.ENABLE_SEARCH_AUTH = 'true'
      process.env.ENABLE_HMAC_AUTH = 'true'

      expect(shouldEnforceAuthentication('/api/search')).toBe(true)
      expect(isAuthenticationEnabled()).toBe(true)
      expect(isHMACEnabled()).toBe(true)
    })

    it('handles missing environment variables (defaults to disabled)', () => {
      delete process.env.ENABLE_SEARCH_AUTH
      delete process.env.ENABLE_HMAC_AUTH

      expect(isAuthenticationEnabled()).toBe(false)
      expect(isHMACEnabled()).toBe(false)
      expect(shouldEnforceAuthentication('/api/search')).toBe(false)
    })

    it('handles invalid environment variable values', () => {
      process.env.ENABLE_SEARCH_AUTH = 'invalid'
      process.env.ENABLE_HMAC_AUTH = 'invalid'

      expect(isAuthenticationEnabled()).toBe(false)
      expect(isHMACEnabled()).toBe(false)
      expect(shouldEnforceAuthentication('/api/search')).toBe(false)
    })
  })

  describe('Replay Cache Management', () => {
    beforeEach(() => {
      clearReplayCache()
    })

    it('tracks cache size correctly', async () => {
      expect(getReplayCacheSize()).toBe(0)

      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)

      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': `sha256=${signature}`,
          'x-timestamp': timestamp.toString(),
          'x-partner-id': TEST_PARTNER_ID
        }
      })

      await verifyRequestHMAC(request)
      expect(getReplayCacheSize()).toBe(1)
    })

    it('handles cache eviction for expired entries', async () => {
      const now = Math.floor(Date.now() / 1000)

      // Create requests with different timestamps
      const oldTimestamp = now - 400 // Expired (beyond 300s window)
      const newTimestamp = now

      const oldSignature = generateHMACSignature('GET', '/api/search', oldTimestamp, '', TEST_SECRET)
      const newSignature = generateHMACSignature('GET', '/api/search', newTimestamp, '', TEST_SECRET)

      const oldRequest = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': `sha256=${oldSignature}`,
          'x-timestamp': oldTimestamp.toString(),
          'x-partner-id': TEST_PARTNER_ID
        }
      })

      const newRequest = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': `sha256=${newSignature}`,
          'x-timestamp': newTimestamp.toString(),
          'x-partner-id': TEST_PARTNER_ID
        }
      })

      // Old request should be rejected due to expired timestamp
      const oldPayload = await verifyRequestHMAC(oldRequest)
      expect(oldPayload).toBeNull()

      // New request should succeed
      const newPayload = await verifyRequestHMAC(newRequest)
      expect(newPayload).not.toBeNull()

      // Cache should only contain the new entry
      expect(getReplayCacheSize()).toBe(1)
    })

    it('stress test: handles 10k cache entries without memory issues', async () => {
      const baseTimestamp = Math.floor(Date.now() / 1000)
      
      // Process in smaller batches to avoid timestamp expiration
      const batchSize = 1000
      const numBatches = 10
      let totalSuccessCount = 0
      
      for (let batch = 0; batch < numBatches; batch++) {
        const promises: Promise<any>[] = []
        const currentTimestamp = Math.floor(Date.now() / 1000) // Fresh timestamp for each batch
        
        for (let i = 0; i < batchSize; i++) {
          const id = batch * batchSize + i
          const signature = generateHMACSignature('GET', `/api/search?id=${id}`, currentTimestamp, '', TEST_SECRET)

          const request = new NextRequest(`http://localhost/api/search?id=${id}`, {
            headers: {
              'x-signature': `sha256=${signature}`,
              'x-timestamp': currentTimestamp.toString(),
              'x-partner-id': TEST_PARTNER_ID
            }
          })

          promises.push(verifyRequestHMAC(request))
        }

        // Process current batch
        const results = await Promise.all(promises)
        const successCount = results.filter(result => result !== null).length
        totalSuccessCount += successCount
        
        // Each batch should succeed completely
        expect(successCount).toBe(batchSize)
      }

      // All should succeed (unique signatures due to different paths)
      expect(totalSuccessCount).toBe(10000)

      // Cache should contain all entries
      expect(getReplayCacheSize()).toBe(10000)

      // Memory usage should be reasonable (this is more of a smoke test)
      const memUsage = process.memoryUsage()
      expect(memUsage.heapUsed).toBeLessThan(100 * 1024 * 1024) // Less than 100MB
    }, 30000) // 30 second timeout for stress test

    it('prevents replay attacks in high-concurrency scenario', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)

      // Create 100 identical requests (replay attack simulation)
      const promises: Promise<any>[] = []
      for (let i = 0; i < 100; i++) {
        const request = new NextRequest('http://localhost/api/search', {
          headers: {
            'x-signature': `sha256=${signature}`,
            'x-timestamp': timestamp.toString(),
            'x-partner-id': TEST_PARTNER_ID
          }
        })

        promises.push(verifyRequestHMAC(request))
      }

      // Process all requests concurrently
      const results = await Promise.all(promises)

      // Only one should succeed, rest should be rejected as replays
      const successCount = results.filter(result => result !== null).length
      expect(successCount).toBe(1)

      // Cache should only contain one entry
      expect(getReplayCacheSize()).toBe(1)
    })

    it('clears cache correctly', async () => {
      // Add some entries
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)

      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': `sha256=${signature}`,
          'x-timestamp': timestamp.toString(),
          'x-partner-id': TEST_PARTNER_ID
        }
      })

      await verifyRequestHMAC(request)
      expect(getReplayCacheSize()).toBe(1)

      // Clear cache
      clearReplayCache()
      expect(getReplayCacheSize()).toBe(0)
    })
  })
})
