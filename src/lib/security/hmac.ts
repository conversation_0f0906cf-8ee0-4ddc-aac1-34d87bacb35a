// src/lib/security/hmac.ts
// HMAC authentication helper with replay protection and partner management

import crypto from 'crypto'
import { NextRequest, NextResponse } from 'next/server'
import { createHMACErrorResponse, AUTH_ERROR_CODES } from './error-responses'

// Configuration constants
const HMAC_ALGORITHM = 'sha256'
const DEFAULT_TIMESTAMP_WINDOW = 300 // 5 minutes
const MIN_SECRET_LENGTH = 32

// Header constants to avoid typos
export const HMAC_HEADERS = {
  SIGNATURE: 'x-signature',
  TIMESTAMP: 'x-timestamp',
  PARTNER_ID: 'x-partner-id',
  NONCE: 'x-nonce',
  VERSION: 'x-version'
} as const

// Supported API version
const SUPPORTED_API_VERSION = '1.0'

// TypeScript interfaces
export interface HMACData {
  signature: string
  timestamp: number
  partnerId: string
  nonce?: string
  version?: string
}

export interface HMACPayload {
  partnerId: string
  timestamp: number
  method: string
  path: string
  isValid: boolean
  nonce?: string
}

export interface HMACConfig {
  algorithm: 'sha256'
  timestampWindow: number
  encoding: 'hex'
  requireNonce: boolean
  enableReplayProtection: boolean
}

// HMACError type now imported from error-responses.ts

export interface HMACValidationResult {
  isValid: boolean
  error?: HMACError
  partnerId?: string
  timestamp?: number
}

// In-memory replay protection (MVP implementation)
// Note: For production scale, migrate to Redis
const recentRequests = new Map<string, number>()

// Automatic cleanup interval for replay cache
let cleanupInterval: NodeJS.Timeout | null = null

// Initialize automatic cleanup (only in non-test environments)
function initializeReplayCleanup() {
  if (process.env.NODE_ENV !== 'test' && !cleanupInterval) {
    // Clean up every minute
    cleanupInterval = setInterval(() => {
      cleanupReplayCache()
    }, 60000)

    // Ensure cleanup on process exit
    process.on('exit', () => {
      if (cleanupInterval) {
        clearInterval(cleanupInterval)
      }
    })
  }
}

// Manual cleanup function
function cleanupReplayCache() {
  const now = Math.floor(Date.now() / 1000)
  const timestampWindow = parseInt(process.env.HMAC_TIMESTAMP_WINDOW || DEFAULT_TIMESTAMP_WINDOW.toString(), 10)
  let cleanedCount = 0

  for (const [key, timestamp] of recentRequests.entries()) {
    if (now - timestamp > timestampWindow) {
      recentRequests.delete(key)
      cleanedCount++
    }
  }

  if (cleanedCount > 0) {
    console.log(`Cleaned ${cleanedCount} expired replay cache entries. Cache size: ${recentRequests.size}`)
  }
}

// Export for testing purposes
export function clearReplayCache(): void {
  recentRequests.clear()
}

// Export for testing cache size
export function getReplayCacheSize(): number {
  return recentRequests.size
}

// Boot-time secret validation
export function validateHMACConfiguration(): void {
  const errors: string[] = []

  // Check if any partner secrets are configured
  const partnerSecrets = Object.keys(process.env)
    .filter(key => key.startsWith('PARTNER_SECRET_'))
    .map(key => ({ key, value: process.env[key] }))

  if (partnerSecrets.length === 0) {
    errors.push('No PARTNER_SECRET_* environment variables found. At least PARTNER_SECRET_DEFAULT is required.')
  }

  // Validate each secret length
  partnerSecrets.forEach(({ key, value }) => {
    if (!value) {
      errors.push(`${key} is defined but empty`)
    } else if (value.length < MIN_SECRET_LENGTH) {
      errors.push(`${key} is too short (${value.length} chars, minimum ${MIN_SECRET_LENGTH})`)
    }
  })

  // Validate timestamp window
  const timestampWindow = process.env.HMAC_TIMESTAMP_WINDOW
  if (timestampWindow && (isNaN(parseInt(timestampWindow)) || parseInt(timestampWindow) <= 0)) {
    errors.push(`HMAC_TIMESTAMP_WINDOW must be a positive number, got: ${timestampWindow}`)
  }

  if (errors.length > 0) {
    console.error('HMAC Configuration Validation Failed:')
    errors.forEach(error => console.error(`  - ${error}`))
    throw new Error(`HMAC configuration validation failed: ${errors.length} error(s) found`)
  }

  console.log(`HMAC configuration validated: ${partnerSecrets.length} partner secret(s) configured`)
}

// Call validation at module load (fail fast)
// Skip validation during build time and tests
if (process.env.NODE_ENV !== 'test' && process.env.NEXT_PHASE !== 'phase-production-build') {
  try {
    validateHMACConfiguration()
    // Initialize automatic replay cache cleanup
    initializeReplayCleanup()
  } catch (error) {
    console.error('HMAC validation failed at startup:', error)
    // In production, log the error but don't crash the server
    // Let the application handle authentication failures gracefully
    if (process.env.NODE_ENV === 'production') {
      console.error('CRITICAL: HMAC authentication will be disabled due to configuration errors')
      console.error('Please check environment variables: PARTNER_SECRET_DEFAULT, HMAC_TIMESTAMP_WINDOW')
      // Don't exit - let the application continue with authentication disabled
    }
  }
}

// Feature flag helpers
export function isAuthenticationEnabled(): boolean {
  return process.env.ENABLE_SEARCH_AUTH === 'true'
}

export function isHMACEnabled(): boolean {
  return process.env.ENABLE_HMAC_AUTH === 'true'
}

export function shouldEnforceAuthentication(endpoint: string): boolean {
  if (!isAuthenticationEnabled()) {
    console.log(`Authentication disabled via feature flag for ${endpoint}`)
    return false
  }
  
  // Allow search suggestions to be public - they're just hints and don't reveal sensitive data
  if (endpoint === '/api/search/suggestions') {
    console.log(`Search suggestions endpoint is public, no authentication required`)
    return false
  }
  
  return true
}

// Core HMAC functions
export function generateHMACSignature(
  method: string,
  path: string,
  timestamp: number,
  body: string = '',
  secret: string
): string {
  // Create SHA256 hash of request body
  const bodyHash = crypto.createHash('sha256').update(body).digest('hex')
  
  // Create message string for signing
  const message = `${method}\n${path}\n${timestamp}\n${bodyHash}`
  
  // Generate HMAC signature
  return crypto
    .createHmac(HMAC_ALGORITHM, secret)
    .update(message)
    .digest('hex')
}

export function verifyHMACSignature(
  signature: string,
  method: string,
  path: string,
  timestamp: number,
  body: string = '',
  secret: string
): boolean {
  try {
    const expectedSignature = generateHMACSignature(method, path, timestamp, body, secret)

    // Ensure both signatures are the same length before comparison
    if (signature.length !== expectedSignature.length) {
      return false
    }

    // Use constant-time comparison to prevent timing attacks
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    )
  } catch (error) {
    console.warn('HMAC signature verification error:', error)
    return false
  }
}

// Request processing functions
export function extractHMACFromRequest(request: NextRequest): HMACData | null {
  const signature = request.headers.get(HMAC_HEADERS.SIGNATURE)
  const timestamp = request.headers.get(HMAC_HEADERS.TIMESTAMP)
  const partnerId = request.headers.get(HMAC_HEADERS.PARTNER_ID)

  if (!signature || !timestamp || !partnerId) {
    return null
  }

  // Remove 'sha256=' prefix if present
  const cleanSignature = signature.replace(/^sha256=/, '')

  // Validate timestamp format
  const timestampNum = parseInt(timestamp, 10)
  if (isNaN(timestampNum)) {
    return null
  }

  return {
    signature: cleanSignature,
    timestamp: timestampNum,
    partnerId,
    nonce: request.headers.get(HMAC_HEADERS.NONCE) || undefined,
    version: request.headers.get(HMAC_HEADERS.VERSION) || undefined
  }
}

// Partner secret management
function getPartnerSecret(partnerId: string): string | null {
  // Try partner-specific secret first
  const secretKey = `PARTNER_SECRET_${partnerId.toUpperCase().replace(/[^A-Z0-9]/g, '_')}`
  let secret = process.env[secretKey]
  
  // Fall back to default secret if partner-specific not found
  if (!secret) {
    secret = process.env.PARTNER_SECRET_DEFAULT
  }
  
  if (!secret) {
    console.error('No secret found for partner (ID redacted for security)')
    return null
  }

  if (secret.length < MIN_SECRET_LENGTH) {
    console.error(`Partner secret too short (minimum ${MIN_SECRET_LENGTH} characters required)`)
    return null
  }
  
  return secret
}

// Get request body for signature verification
async function getRequestBody(request: NextRequest): Promise<string> {
  try {
    if (request.method === 'GET' || request.method === 'HEAD') {
      return ''
    }
    
    // Clone request to avoid consuming the body
    const clonedRequest = request.clone()
    const contentType = clonedRequest.headers.get('content-type') || ''
    
    if (contentType.includes('application/json')) {
      const body = await clonedRequest.json()
      return JSON.stringify(body)
    }
    
    if (contentType.includes('application/x-www-form-urlencoded')) {
      const formData = await clonedRequest.formData()
      return new URLSearchParams(formData as any).toString()
    }
    
    // For other content types, get raw text
    return await clonedRequest.text()
  } catch (error) {
    console.warn('Failed to parse request body for HMAC verification:', error)
    return ''
  }
}

// Replay protection
function isReplayRequest(signature: string, timestamp: number): boolean {
  const key = `${signature}-${timestamp}`

  // Check if this request was seen before
  if (recentRequests.has(key)) {
    return true // Replay detected
  }

  // Store this request
  recentRequests.set(key, timestamp)
  return false
}

// Generate trace ID for request correlation
export function generateTraceId(method?: string): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 9)
  const methodPrefix = method ? `${method.toLowerCase()}-` : ''
  const shortHash = timestamp.toString(36).substring(-4) // Last 4 chars of base36 timestamp
  return `hmac-${methodPrefix}${shortHash}-${random}`
}

// Enhanced request verification with detailed error tracking
export async function verifyRequestHMAC(request: NextRequest): Promise<HMACPayload | null> {
  const hmacData = extractHMACFromRequest(request)
  if (!hmacData) {
    return null
  }

  // Validate API version if provided
  if (hmacData.version && hmacData.version !== SUPPORTED_API_VERSION) {
    console.warn(`Unsupported API version: ${hmacData.version}, supported: ${SUPPORTED_API_VERSION}`)
    return null
  }

  // Validate timestamp window
  const now = Math.floor(Date.now() / 1000)
  const timestampWindow = parseInt(process.env.HMAC_TIMESTAMP_WINDOW || DEFAULT_TIMESTAMP_WINDOW.toString(), 10)

  if (Math.abs(now - hmacData.timestamp) > timestampWindow) {
    console.warn(`HMAC timestamp expired: ${hmacData.timestamp}, now: ${now}, window: ${timestampWindow}s, skew: ${now - hmacData.timestamp}s`)
    return null
  }

  // Check for replay attacks
  if (isReplayRequest(hmacData.signature, hmacData.timestamp)) {
    console.warn(`Replay attack detected for partner: ${hmacData.partnerId}`)
    return null
  }

  // Get partner secret
  const partnerSecret = getPartnerSecret(hmacData.partnerId)
  if (!partnerSecret) {
    console.warn(`Unknown partner ID: ${hmacData.partnerId}`)
    return null
  }

  // Get request body for signature verification
  const body = await getRequestBody(request)
  const { pathname } = new URL(request.url)

  // Verify signature
  const isValid = verifyHMACSignature(
    hmacData.signature,
    request.method,
    pathname,
    hmacData.timestamp,
    body,
    partnerSecret
  )

  if (!isValid) {
    console.warn(`HMAC signature verification failed for partner: ${hmacData.partnerId}`)
    return null
  }

  return {
    partnerId: hmacData.partnerId,
    timestamp: hmacData.timestamp,
    method: request.method,
    path: pathname,
    isValid: true,
    nonce: hmacData.nonce
  }
}

// Enhanced verification with specific error codes
export async function verifyRequestHMACWithError(request: NextRequest): Promise<{ success: boolean; error?: HMACError; payload?: HMACPayload }> {
  const hmacData = extractHMACFromRequest(request)
  if (!hmacData) {
    return { success: false, error: AUTH_ERROR_CODES.MISSING_SIGNATURE }
  }

  // Validate API version if provided
  if (hmacData.version && hmacData.version !== SUPPORTED_API_VERSION) {
    return { success: false, error: AUTH_ERROR_CODES.INVALID_FORMAT }
  }

  // Validate timestamp window
  const now = Math.floor(Date.now() / 1000)
  const timestampWindow = parseInt(process.env.HMAC_TIMESTAMP_WINDOW || DEFAULT_TIMESTAMP_WINDOW.toString(), 10)

  if (Math.abs(now - hmacData.timestamp) > timestampWindow) {
    return { success: false, error: AUTH_ERROR_CODES.EXPIRED_TIMESTAMP }
  }

  // Check for replay attacks
  if (isReplayRequest(hmacData.signature, hmacData.timestamp)) {
    return { success: false, error: AUTH_ERROR_CODES.REPLAY_DETECTED }
  }

  // Get partner secret
  const partnerSecret = getPartnerSecret(hmacData.partnerId)
  if (!partnerSecret) {
    return { success: false, error: AUTH_ERROR_CODES.UNKNOWN_PARTNER }
  }

  // Get request body for signature verification
  const body = await getRequestBody(request)
  const { pathname } = new URL(request.url)

  // Verify signature
  const isValid = verifyHMACSignature(
    hmacData.signature,
    request.method,
    pathname,
    hmacData.timestamp,
    body,
    partnerSecret
  )

  if (!isValid) {
    return { success: false, error: AUTH_ERROR_CODES.INVALID_SIGNATURE }
  }

  const payload: HMACPayload = {
    partnerId: hmacData.partnerId,
    timestamp: hmacData.timestamp,
    method: request.method,
    path: pathname,
    isValid: true,
    nonce: hmacData.nonce
  }

  return { success: true, payload }
}

// Create HMAC headers for outgoing requests
export function createHMACHeaders(
  method: string,
  path: string,
  partnerId: string,
  body: string = '',
  secret?: string,
  options?: { includeVersion?: boolean; nonce?: string }
): Record<string, string> {
  const timestamp = Math.floor(Date.now() / 1000)

  // Use provided secret or get from environment
  let partnerSecret: string
  if (secret) {
    partnerSecret = secret
  } else {
    const envSecret = getPartnerSecret(partnerId)
    if (!envSecret) {
      throw new Error(`No secret available for partner: ${partnerId}`)
    }
    partnerSecret = envSecret
  }

  const signature = generateHMACSignature(method, path, timestamp, body, partnerSecret)

  const headers: Record<string, string> = {
    'X-Signature': `sha256=${signature}`,
    'X-Timestamp': timestamp.toString(),
    'X-Partner-ID': partnerId,
    'Content-Type': 'application/json'
  }

  // Add optional headers
  if (options?.includeVersion) {
    headers['X-Version'] = SUPPORTED_API_VERSION
  }

  if (options?.nonce) {
    headers['X-Nonce'] = options.nonce
  }

  return headers
}

// Re-export HMACError type for backward compatibility
export type HMACError = AUTH_ERROR_CODES

// Security event logging
interface SecurityEvent {
  type: 'HMAC_AUTH_SUCCESS' | 'HMAC_AUTH_FAILURE' | 'JWT_AUTH_SUCCESS' | 'JWT_AUTH_FAILURE'
  endpoint: string
  method: string
  partnerId?: string
  ip: string
  timestamp: string
  traceId: string
  error?: HMACError
  errorMessage?: string
}

export function logSecurityEvent(event: SecurityEvent): void {
  // Downgrade log level for suggestions endpoint to DEBUG to reduce log noise
  let level = event.type.includes('FAILURE') ? 'WARN' : 'INFO'
  if (event.endpoint === '/api/search/suggestions' && event.type.includes('FAILURE')) {
    level = 'DEBUG'
  }
  
  const logEntry = {
    level,
    message: `Security Event: ${event.type}`,
    traceId: event.traceId,
    endpoint: event.endpoint,
    method: event.method,
    partnerId: event.partnerId,
    ip: event.ip,
    timestamp: event.timestamp,
    error: event.error,
    errorMessage: event.errorMessage
  }

  // Only log DEBUG level in development
  if (level === 'DEBUG' && process.env.NODE_ENV === 'production') {
    return
  }

  // Log to CloudWatch via console (structured logging)
  console.log(JSON.stringify(logEntry))
}

// Get client IP from request
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const cfIP = request.headers.get('cf-connecting-ip')

  return cfIP || realIP || forwarded?.split(',')[0].trim() || 'unknown'
}
