/**
 * IP Allowlist Middleware for Cashback Deals
 * 
 * Provides CIDR-based IP validation with IPv4/IPv6 support, feature flags,
 * and self-lockout prevention as per PR3 technical specifications.
 * 
 * <AUTHOR> Agent
 * @date 2025-07-13
 */

import { NextRequest, NextResponse } from 'next/server';

// ============================================================================
// Types and Interfaces
// ============================================================================

export interface IPAllowlistConfig {
  enabled: boolean;
  allowedCIDRs: string[];
  logViolations: boolean;
  blockByDefault: boolean;
  includeDebug: boolean;
}

export interface IPValidationResult {
  allowed: boolean;
  ip: string;
  reason: 'allowed_by_rule' | 'blocked_by_default' | 'invalid_ip' | 'disabled';
  matchedRule?: string;
  debugInfo?: {
    headers: Record<string, string>;
    extractedIPs: string[];
  };
}

export interface CIDRRange {
  network: string;
  prefix: number;
  isIPv6: boolean;
}

// ============================================================================
// Environment Configuration
// ============================================================================

/**
 * Load IP allowlist configuration from environment variables
 */
export function getIPAllowlistConfig(): IPAllowlistConfig {
  const enabled = process.env.ENABLE_IP_ALLOWLIST === 'true';
  const cidrsString = process.env.IP_ALLOWLIST_CIDRS || '';
  const logViolations = process.env.IP_ALLOWLIST_LOG_VIOLATIONS !== 'false';
  const blockByDefault = process.env.IP_ALLOWLIST_BLOCK_BY_DEFAULT !== 'false';
  const includeDebug = process.env.IP_ALLOWLIST_INCLUDE_DEBUG === 'true';

  // Parse CIDR ranges from comma-separated string
  const allowedCIDRs = cidrsString
    .split(',')
    .map(cidr => cidr.trim())
    .filter(cidr => cidr.length > 0);

  return {
    enabled,
    allowedCIDRs,
    logViolations,
    blockByDefault,
    includeDebug,
  };
}

// ============================================================================
// Build-time Validation (Fail-fast approach)
// ============================================================================

/**
 * Validate IP allowlist configuration at build time
 * Throws error if critical validation fails (prevents deployment)
 */
export function validateIPAllowlistConfig(config: IPAllowlistConfig): string[] {
  const errors: string[] = [];

  // Critical: Fail build if no CIDR ranges configured when enabled
  if (config.enabled && config.allowedCIDRs.length === 0) {
    throw new Error('FATAL: No CIDR ranges configured - risk of complete lockout');
  }

  // Validate each CIDR range
  config.allowedCIDRs.forEach(cidr => {
    try {
      parseCIDR(cidr);
    } catch (error) {
      const errorMessage = `FATAL: Invalid CIDR range: ${cidr}`;
      console.error(errorMessage, error);
      throw new Error(errorMessage);
    }
  });

  // Validate office IP ranges are included (self-lockout prevention)
  const requiredOfficeRanges = ['127.0.0.1/32', '10.0.0.0/8'];
  const hasOfficeRange = requiredOfficeRanges.some(required =>
    config.allowedCIDRs.some(cidr => cidr === required || cidr.includes('10.0.0.0/8'))
  );

  if (config.enabled && !hasOfficeRange) {
    console.warn('WARNING: No office IP ranges detected. Consider adding 10.0.0.0/8 or 127.0.0.1/32');
  }

  return errors;
}

// ============================================================================
// IP Address Validation
// ============================================================================

/**
 * Validate IPv4 address format
 */
export function isValidIPv4(ip: string): boolean {
  const ipv4Regex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
  const match = ip.match(ipv4Regex);
  
  if (!match) return false;
  
  // Check each octet is 0-255
  for (let i = 1; i <= 4; i++) {
    const octet = parseInt(match[i], 10);
    if (octet < 0 || octet > 255) return false;
  }
  
  return true;
}

/**
 * Validate IPv6 address format (supports zero compression per RFC 4291)
 */
export function isValidIPv6(ip: string): boolean {
  // More comprehensive IPv6 validation
  // Handle various IPv6 formats including zero compression

  // Basic format check
  if (!/^[0-9a-fA-F:]+$/.test(ip)) {
    return false;
  }

  // Special cases
  if (ip === '::') return true; // All zeros
  if (ip === '::1') return true; // Loopback

  // Check for double colon (zero compression)
  const doubleColonCount = (ip.match(/::/g) || []).length;
  if (doubleColonCount > 1) return false; // Only one :: allowed

  // Split by double colon if present
  let parts: string[];
  if (doubleColonCount === 1) {
    const [left, right] = ip.split('::');
    const leftParts = left ? left.split(':') : [];
    const rightParts = right ? right.split(':') : [];
    const totalParts = leftParts.length + rightParts.length;

    // With zero compression, total parts should be less than 8
    if (totalParts >= 8) return false;

    parts = [...leftParts, ...rightParts];
  } else {
    // No zero compression, should have exactly 8 parts
    parts = ip.split(':');
    if (parts.length !== 8) return false;
  }

  // Validate each part (should be 1-4 hex digits)
  return parts.every(part => {
    if (part === '') return true; // Empty parts are OK in zero compression context
    return /^[0-9a-fA-F]{1,4}$/.test(part);
  });
}

/**
 * Parse CIDR notation into network and prefix
 */
export function parseCIDR(cidr: string): CIDRRange {
  const parts = cidr.split('/');
  if (parts.length !== 2) {
    throw new Error(`Invalid CIDR format: ${cidr}`);
  }

  const [network, prefixStr] = parts;
  const prefix = parseInt(prefixStr, 10);

  // Determine if IPv4 or IPv6
  const isIPv6 = network.includes(':');
  
  if (isIPv6) {
    if (!isValidIPv6(network)) {
      throw new Error(`Invalid IPv6 address: ${network}`);
    }
    if (prefix < 0 || prefix > 128) {
      throw new Error(`Invalid IPv6 prefix: ${prefix}`);
    }
  } else {
    if (!isValidIPv4(network)) {
      throw new Error(`Invalid IPv4 address: ${network}`);
    }
    if (prefix < 0 || prefix > 32) {
      throw new Error(`Invalid IPv4 prefix: ${prefix}`);
    }
  }

  return { network, prefix, isIPv6 };
}

/**
 * Check if IP address is within CIDR range
 * Note: For production use, recommend ipaddr.js for comprehensive IPv6 support
 */
export function isIPInCIDR(ip: string, cidr: string): boolean {
  try {
    const cidrRange = parseCIDR(cidr);
    
    // IPv6 support (simplified - recommend ipaddr.js for production)
    if (cidrRange.isIPv6) {
      // Basic IPv6 matching - for full support use ipaddr.js
      if (!isValidIPv6(ip)) return false;
      
      // Simplified IPv6 CIDR matching
      // For production, use: import { isValid, process } from 'ipaddr.js'
      return ip.startsWith(cidrRange.network.split('::')[0]);
    }
    
    // IPv4 CIDR matching
    if (!isValidIPv4(ip)) return false;
    
    const ipNum = ipToNumber(ip);
    const networkNum = ipToNumber(cidrRange.network);
    const mask = (0xffffffff << (32 - cidrRange.prefix)) >>> 0;
    
    return (ipNum & mask) === (networkNum & mask);
  } catch (error) {
    console.error('Error checking IP in CIDR:', error);
    return false;
  }
}

/**
 * Convert IPv4 address to number for CIDR calculations
 */
function ipToNumber(ip: string): number {
  return ip.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet, 10), 0) >>> 0;
}

// ============================================================================
// Client IP Extraction
// ============================================================================

/**
 * Extract client IP from request headers with proper precedence
 * Priority: X-Forwarded-For → X-Real-IP → CF-Connecting-IP → fallback
 */
export function getClientIP(request: NextRequest): string {
  // 1. X-Forwarded-For (first IP in comma-separated list)
  const xForwardedFor = request.headers.get('X-Forwarded-For');
  if (xForwardedFor) {
    const firstIP = xForwardedFor.split(',')[0].trim();
    if (firstIP) return firstIP;
  }

  // 2. X-Real-IP (single IP)
  const xRealIP = request.headers.get('X-Real-IP');
  if (xRealIP) return xRealIP.trim();

  // 3. CF-Connecting-IP (Cloudflare)
  const cfConnectingIP = request.headers.get('CF-Connecting-IP');
  if (cfConnectingIP) return cfConnectingIP.trim();

  // 4. Fallback for development
  return '127.0.0.1';
}

// ============================================================================
// IP Allowlist Validation
// ============================================================================

/**
 * Validate IP against allowlist configuration
 */
export function validateIPAllowlist(ip: string, config: IPAllowlistConfig): IPValidationResult {
  // If disabled, allow all traffic
  if (!config.enabled) {
    return {
      allowed: true,
      ip,
      reason: 'disabled',
    };
  }

  // Validate IP format
  if (!isValidIPv4(ip) && !isValidIPv6(ip)) {
    return {
      allowed: false,
      ip,
      reason: 'invalid_ip',
    };
  }

  // Check against allowed CIDR ranges
  for (const cidr of config.allowedCIDRs) {
    if (isIPInCIDR(ip, cidr)) {
      return {
        allowed: true,
        ip,
        reason: 'allowed_by_rule',
        matchedRule: cidr,
      };
    }
  }

  // Block by default if no rules match
  return {
    allowed: false,
    ip,
    reason: 'blocked_by_default',
  };
}

// ============================================================================
// Middleware Application
// ============================================================================

/**
 * Apply IP allowlist middleware to request
 * Returns null if allowed, NextResponse with 403 if blocked
 */
export function applyIPAllowlist(request: NextRequest): NextResponse | null {
  const config = getIPAllowlistConfig();
  
  // Skip if disabled
  if (!config.enabled) {
    return null;
  }

  const clientIP = getClientIP(request);
  const result = validateIPAllowlist(clientIP, config);

  // Log violations if enabled
  if (config.logViolations && !result.allowed) {
    const logData = {
      timestamp: new Date().toISOString(),
      ip: clientIP,
      url: request.url,
      method: request.method,
      reason: result.reason,
      userAgent: request.headers.get('User-Agent'),
    };
    
    console.warn('[IP Allowlist] Blocked request:', JSON.stringify(logData));
  }

  // Allow if validation passed
  if (result.allowed) {
    return null;
  }

  // Block with 403 response
  const errorResponse = {
    error: 'IP_NOT_ALLOWED',
    message: 'Your IP address is not authorized to access this resource',
    timestamp: new Date().toISOString(),
    ...(config.includeDebug && {
      debug: {
        ip: clientIP,
        reason: result.reason,
      },
    }),
  };

  return NextResponse.json(errorResponse, { status: 403 });
}

// ============================================================================
// Health Check
// ============================================================================

/**
 * Health check for IP allowlist functionality
 */
export async function healthCheckIPAllowlist(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  details: {
    configLoaded: boolean;
    cidrRulesValid: boolean;
    performanceOk: boolean;
    lastError?: string;
  };
}> {
  try {
    const config = getIPAllowlistConfig();
    const errors = validateIPAllowlistConfig(config);
    
    // Performance test
    const start = performance.now();
    validateIPAllowlist('**********', config);
    const duration = performance.now() - start;
    
    return {
      status: errors.length === 0 && duration < 2 ? 'healthy' : 'degraded',
      details: {
        configLoaded: true,
        cidrRulesValid: errors.length === 0,
        performanceOk: duration < 2, // 2ms target
      },
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      details: {
        configLoaded: false,
        cidrRulesValid: false,
        performanceOk: false,
        lastError: error instanceof Error ? error.message : 'Unknown error',
      },
    };
  }
}
