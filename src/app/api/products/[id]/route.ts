/**
 * Refactored Product Detail API Route - Phase 2
 * 
 * This route has been refactored to use the shared server-side data layer
 * for improved security, performance, and maintainability.
 * 
 * Key improvements:
 * - Uses shared data layer functions instead of direct Supabase queries
 * - Eliminates public key usage in favor of secure server-side access
 * - Consistent error handling and response formats
 * - Better caching strategy
 * - Reduced code duplication
 * - Supports both UUID and slug-based lookups
 */

import { NextRequest, NextResponse } from 'next/server'
import { getProduct, getProductWithSimilar, getProductBySlug } from '@/lib/data'
import type { ApiResponse, ProductResponse } from '@/lib/data/types'
import { validateIdParameter } from '@/lib/utils'
import { applyRateLimit, rateLimits } from '@/lib/rateLimiter'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';

/**
 * GET /api/products/[id]
 * 
 * Fetches a single product with similar products
 * 
 * Path Parameters:
 * - id: Product ID (UUID) or slug
 * 
 * Returns:
 * - product: Complete product details with all relationships
 * - similar_products: Array of similar products in the same category
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<ProductResponse>>> {
  const startTime = Date.now()

  // Apply rate limiting
  const rateLimitResponse = applyRateLimit(request, rateLimits.product)
  if (rateLimitResponse) {
    return rateLimitResponse as NextResponse<ApiResponse<ProductResponse>>
  }

  try {
    // Extract and validate the ID parameter
    const { id } = await params

    if (!id) {
      return NextResponse.json({
        data: null,
        error: 'Product ID is required',
      }, { status: 400 })
    }

    // Validate and sanitize the ID parameter
    const validation = validateIdParameter(id)
    if (!validation.isValid) {
      return NextResponse.json({
        data: null,
        error: 'Invalid product ID format. Must be a valid UUID or slug.',
      }, { status: 400 })
    }

    const sanitizedId = validation.sanitized
    const isUUID = validation.isUUID
    let productId = sanitizedId

    const supabase = createServerSupabaseReadOnlyClient();

    // If it's a slug, we need to find the product first to get the UUID
    if (!isUUID) {
      const productBySlug = await getProductBySlug(supabase, sanitizedId)
      if (!productBySlug) {
        return NextResponse.json({
          data: null,
          error: 'Product not found',
        }, { status: 404 })
      }
      productId = productBySlug.id
    }
    
    // Fetch product with similar products using shared data layer
    const result = await getProductWithSimilar(supabase, productId)
    
    if (!result) {
      return NextResponse.json({
        data: null,
        error: 'Product not found',
      }, { status: 404 })
    }
    
    // Create response in the expected format
    const response: ApiResponse<ProductResponse> = {
      data: result,
      error: null,
    }
    
    // Create Next.js response with proper caching headers
    const nextResponse = NextResponse.json(response)
    
    // Set cache headers for optimal performance
    // Individual products can be cached longer since they change less frequently
    nextResponse.headers.set(
      'Cache-Control', 
      'public, s-maxage=1800, stale-while-revalidate=60'
    )
    
    // Add CORS headers for API access
    nextResponse.headers.set('Access-Control-Allow-Origin', '*')
    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS')
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type')
    
    // Add performance timing header for monitoring
    nextResponse.headers.set('X-Response-Time', `${Date.now() - startTime}ms`)
    
    return nextResponse
    
  } catch (error) {
    console.error('Error in product detail API route:', error)
    
    // Return standardized error response
    const errorResponse: ApiResponse<ProductResponse> = {
      data: null,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    }
    
    return NextResponse.json(errorResponse, { status: 500 })
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}

/**
 * Runtime configuration
 * Using Node.js runtime for server-side data layer compatibility
 */
export const runtime = 'nodejs'

/**
 * Route segment config for caching
 * Individual products can be cached longer
 */
export const revalidate = 1800 // Revalidate every 30 minutes
