/**
 * Refactored Brand Detail API Route - Phase 2A Step 1
 * 
 * This route has been refactored to use the shared server-side data layer
 * for improved security, performance, and maintainability.
 * 
 * Key improvements:
 * - Uses shared data layer functions instead of direct Supabase queries
 * - Eliminates public key usage in favor of secure server-side access
 * - Consistent error handling and response formats
 * - Better caching strategy
 * - Reduced code duplication
 * - Supports both UUID and slug-based lookups
 */

import { NextRequest, NextResponse } from 'next/server'
import { getBrandWithDetails, getBrandBySlug } from '@/lib/data'
import type { ApiResponse, BrandResponse } from '@/lib/data/types'
import { validateIdParameter } from '@/lib/utils'
import { applyRateLimit, rateLimits } from '@/lib/rateLimiter'

import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';

/**
 * GET /api/brands/[id]
 * 
 * Fetches a single brand with detailed information including products and promotions
 * 
 * Path Parameters:
 * - id: Brand ID (UUID) or slug
 * 
 * Returns:
 * - brand: Complete brand details
 * - featured_products: Array of featured products for this brand
 * - active_promotions: Array of active promotions for this brand
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<BrandResponse>>> {
  const startTime = Date.now()

  // Apply rate limiting
  const rateLimitResponse = applyRateLimit(request, rateLimits.brands)
  if (rateLimitResponse) {
    return rateLimitResponse as NextResponse<ApiResponse<BrandResponse>>
  }

  try {
    // Extract and validate the ID parameter
    const { id } = await params

    if (!id) {
      return NextResponse.json({
        data: null,
        error: 'Brand ID is required',
      }, { status: 400 })
    }

    // Validate and sanitize the ID parameter
    const validation = validateIdParameter(id)
    if (!validation.isValid) {
      return NextResponse.json({
        data: null,
        error: 'Invalid brand ID format. Must be a valid UUID or slug.',
      }, { status: 400 })
    }

    const supabase = createServerSupabaseReadOnlyClient();
    const sanitizedId = validation.sanitized
    const isUUID = validation.isUUID
    let brandId = sanitizedId

    // If it's a slug, we need to find the brand first to get the UUID
    if (!isUUID) {
      const brandBySlug = await getBrandBySlug(supabase, sanitizedId)
      if (!brandBySlug) {
        return NextResponse.json({
          data: null,
          error: 'Brand not found',
        }, { status: 404 })
      }
      brandId = brandBySlug.id
    }
    
    // Fetch brand with detailed information using shared data layer
    const result = await getBrandWithDetails(supabase, brandId)
    
    if (!result) {
      return NextResponse.json({
        data: null,
        error: 'Brand not found',
      }, { status: 404 })
    }
    
    // Create response in the expected format
    const response: ApiResponse<BrandResponse> = {
      data: result,
      error: null,
    }
    
    // Create Next.js response with proper caching headers
    const nextResponse = NextResponse.json(response)
    
    // Set cache headers for optimal performance
    // Brand details can be cached longer since they change less frequently
    nextResponse.headers.set(
      'Cache-Control', 
      'public, s-maxage=1800, stale-while-revalidate=300'
    )
    
    // Add CORS headers for API access
    nextResponse.headers.set('Access-Control-Allow-Origin', '*')
    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS')
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type')
    
    // Add performance timing header for monitoring
    nextResponse.headers.set('X-Response-Time', `${Date.now() - startTime}ms`)
    
    return nextResponse
    
  } catch (error) {
    console.error('Error in brand detail API route:', error)
    
    // Return standardized error response
    const errorResponse: ApiResponse<BrandResponse> = {
      data: null,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    }
    
    return NextResponse.json(errorResponse, { status: 500 })
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}

/**
 * Runtime configuration
 * Using Node.js runtime for server-side data layer compatibility
 */
export const runtime = 'nodejs'

/**
 * Route segment config for caching
 * Brand details can be cached longer
 */
export const revalidate = 1800 // Revalidate every 30 minutes
