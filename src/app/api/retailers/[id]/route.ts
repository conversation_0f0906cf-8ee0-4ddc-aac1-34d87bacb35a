/**
 * Retailer Detail API Route - Phase 2C Implementation
 * 
 * This route provides detailed retailer information including featured products
 * and active offers count. Supports both UUID and slug-based lookups.
 * 
 * Key features:
 * - Uses shared server-side data layer for security and performance
 * - Supports both UUID and slug lookups
 * - Includes featured products from the retailer
 * - Provides active offers count
 * - Consistent error handling and response formats
 * - Proper caching strategy
 */

import { NextRequest, NextResponse } from 'next/server'
import { getRetailerWithProducts, getRetailerBySlug } from '@/lib/data'
import type { ApiResponse, RetailerResponse } from '@/lib/data/types'
import { validateIdParameter } from '@/lib/utils'
import { applyRateLimit, rateLimits } from '@/lib/rateLimiter'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';

/**
 * GET /api/retailers/[id]
 * 
 * Retrieves detailed retailer information with featured products
 * 
 * Path Parameters:
 * - id: Retailer UUID or slug
 * 
 * Returns:
 * - retailer: Detailed retailer information
 * - featured_products: Array of featured products from this retailer
 * - active_offers_count: Total number of active offers from this retailer
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  const startTime = Date.now()

  // Apply rate limiting
  const rateLimitResponse = applyRateLimit(request, rateLimits.retailers)
  if (rateLimitResponse) {
    return rateLimitResponse
  }

  try {
    // Extract and validate the ID parameter
    const { id } = await params

    if (!id) {
      const errorResponse: ApiResponse<null> = {
        data: null,
        error: 'Retailer ID is required',
      }
      return NextResponse.json(errorResponse, { status: 400 })
    }

    // Validate and sanitize the ID parameter
    const validation = validateIdParameter(id)
    if (!validation.isValid) {
      const errorResponse: ApiResponse<null> = {
        data: null,
        error: 'Invalid retailer ID format. Must be a valid UUID or slug.',
      }
      return NextResponse.json(errorResponse, { status: 400 })
    }

    const sanitizedId = validation.sanitized
    const isUUID = validation.isUUID
    let retailerData: RetailerResponse | null = null

    const supabase = createServerSupabaseReadOnlyClient();

    // Determine if ID is UUID or slug and fetch accordingly
    if (isUUID) {
      // Fetch by UUID
      retailerData = await getRetailerWithProducts(supabase, sanitizedId)
    } else {
      // Fetch by slug - first get retailer, then get full data
      const retailer = await getRetailerBySlug(supabase, sanitizedId)
      if (retailer) {
        retailerData = await getRetailerWithProducts(supabase, retailer.id)
      }
    }

    if (!retailerData) {
      const errorResponse: ApiResponse<null> = {
        data: null,
        error: 'Retailer not found',
      }

      const nextResponse = NextResponse.json(errorResponse, { status: 404 })
      
      // Set CORS headers
      nextResponse.headers.set('Access-Control-Allow-Origin', '*')
      nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS')
      nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type')

      return nextResponse
    }

    // Prepare API response
    const response: ApiResponse<RetailerResponse> = {
      data: retailerData,
      error: null,
    }

    // Create response with proper headers
    const nextResponse = NextResponse.json(response, { status: 200 })

    // Set caching headers - retailer details can be cached longer
    nextResponse.headers.set('Cache-Control', 'public, max-age=600, s-maxage=1800, stale-while-revalidate=300')
    
    // Set CORS headers
    nextResponse.headers.set('Access-Control-Allow-Origin', '*')
    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS')
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type')

    // Add performance headers
    const duration = Date.now() - startTime
    nextResponse.headers.set('X-Response-Time', `${duration}ms`)
    nextResponse.headers.set('X-Cache-Status', 'MISS')

    return nextResponse

  } catch (error) {
    console.error('Error in retailer detail API route:', error)

    const errorResponse: ApiResponse<null> = {
      data: null,
      error: error instanceof Error ? error.message : 'Internal server error',
    }

    const nextResponse = NextResponse.json(errorResponse, { status: 500 })
    
    // Set CORS headers even for errors
    nextResponse.headers.set('Access-Control-Allow-Origin', '*')
    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS')
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type')

    // Add performance headers
    const duration = Date.now() - startTime
    nextResponse.headers.set('X-Response-Time', `${duration}ms`)

    return nextResponse
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}

/**
 * Runtime configuration
 * Using Node.js runtime for server-side data layer compatibility
 */
export const runtime = 'nodejs'

/**
 * Route segment config for caching
 * Individual retailer details can be cached longer
 */
export const revalidate = 1800 // Revalidate every 30 minutes
