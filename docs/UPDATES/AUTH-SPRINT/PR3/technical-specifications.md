# PR3: Sentry Cleanup & IP Allowlist - Technical Specifications

**Date:** July 13, 2025  
**Sprint:** Auth-Sprint Phase 1  
**Version:** 1.0  

## 🏗️ Architecture Overview

### Component Diagram
```mermaid
graph TB
    subgraph "Client Request"
        C[Client IP Request]
    end
    
    subgraph "Next.js API Route"
        A[API Route Handler]
        M[IP Allowlist Middleware]
        V[IP Validator]
        L[Security Logger]
    end
    
    subgraph "Configuration"
        E[Environment Variables]
        F[Feature Flags]
        R[CIDR Rules]
    end
    
    subgraph "Response"
        OK[200 OK - Allowed]
        DENY[403 Forbidden - Blocked]
    end
    
    C --> A
    A --> M
    M --> V
    V --> E
    V --> R
    M --> L
    V -->|Allowed| OK
    V -->|Blocked| DENY
    L --> E
```

## 📋 Core Interfaces

### 1. IP Allowlist Configuration Interface

```typescript
/**
 * Configuration interface for IP allowlist functionality
 */
export interface IPAllowlistConfig {
  /** Whether IP allowlist enforcement is enabled */
  enabled: boolean;
  
  /** Array of CIDR notation ranges (e.g., ['10.0.0.0/8', '**********/12']) */
  allowedCIDRs: string[];
  
  /** Whether to log IP violation attempts */
  logViolations: boolean;
  
  /** Whether to block by default if no rules match */
  blockByDefault: boolean;
  
  /** Custom error message for blocked requests */
  customErrorMessage?: string;
  
  /** Whether to include detailed error information in response */
  includeDebugInfo: boolean;
}
```

### 2. IP Validation Result Interface

```typescript
/**
 * Result of IP validation against allowlist
 */
export interface IPValidationResult {
  /** Whether the IP is allowed */
  allowed: boolean;
  
  /** The client IP that was checked */
  clientIP: string;
  
  /** Which CIDR rule matched (if any) */
  matchedRule?: string;
  
  /** Reason for allowing/blocking */
  reason: 'allowed_by_rule' | 'blocked_by_default' | 'disabled' | 'invalid_ip';
  
  /** Additional context for logging */
  context: {
    userAgent?: string;
    requestPath: string;
    timestamp: Date;
  };
}
```

### 3. Security Event Interface

```typescript
/**
 * Security event for IP allowlist violations
 */
export interface IPSecurityEvent {
  /** Type of security event */
  type: 'IP_ALLOWLIST_VIOLATION' | 'IP_ALLOWLIST_ALLOWED' | 'IP_ALLOWLIST_CONFIG_ERROR';
  
  /** Severity level */
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
  
  /** Event details */
  details: {
    clientIP: string;
    requestPath: string;
    userAgent?: string;
    matchedRule?: string;
    reason: string;
  };
  
  /** Timestamp of event */
  timestamp: Date;
  
  /** Unique trace ID for correlation */
  traceId: string;
}
```

## 🔧 Core Functions Specification

### 1. IP Validation Functions

```typescript
/**
 * Check if an IP address is within a CIDR range
 * @param ip - IP address to check (IPv4 or IPv6)
 * @param cidr - CIDR notation (e.g., '10.0.0.0/8')
 * @returns boolean indicating if IP is in range
 */
export function isIPInCIDR(ip: string, cidr: string): boolean;

/**
 * Validate IP address against allowlist configuration
 * @param clientIP - IP address to validate
 * @param config - IP allowlist configuration
 * @returns Validation result with details
 */
export function validateIPAllowlist(
  clientIP: string, 
  config: IPAllowlistConfig
): IPValidationResult;

/**
 * Extract client IP from Next.js request headers
 * @param request - NextRequest object
 * @returns Client IP address (fallback to localhost for development)
 */
export function getClientIP(request: NextRequest): string;
```

### 2. Middleware Functions

```typescript
/**
 * Create IP allowlist middleware for API routes
 * @param config - IP allowlist configuration
 * @returns NextResponse with 403 if blocked, null if allowed
 */
export function createIPAllowlistMiddleware(
  config: IPAllowlistConfig
): (request: NextRequest) => NextResponse | null;

/**
 * Apply IP allowlist validation to request
 * @param request - NextRequest object
 * @param config - Optional custom configuration
 * @returns NextResponse if blocked, null if allowed
 */
export function applyIPAllowlist(
  request: NextRequest,
  config?: IPAllowlistConfig
): NextResponse | null;
```

### 3. Configuration Functions

```typescript
/**
 * Load IP allowlist configuration from environment variables
 * @returns Validated IP allowlist configuration
 */
export function getIPAllowlistConfig(): IPAllowlistConfig;

/**
 * Validate IP allowlist configuration
 * @param config - Configuration to validate
 * @returns Array of validation errors (empty if valid)
 */
export function validateIPAllowlistConfig(config: IPAllowlistConfig): string[];

/**
 * Parse CIDR list from environment variable string
 * @param cidrString - Comma-separated CIDR list
 * @returns Array of validated CIDR strings
 */
export function parseCIDRList(cidrString: string): string[];
```

### 4. Logging Functions

```typescript
/**
 * Log IP allowlist security event
 * @param event - Security event to log
 */
export function logIPSecurityEvent(event: IPSecurityEvent): void;

/**
 * Create security event for IP validation result
 * @param result - IP validation result
 * @param request - Next.js request object
 * @returns Security event object
 */
export function createIPSecurityEvent(
  result: IPValidationResult,
  request: NextRequest
): IPSecurityEvent;
```

## 🌍 Environment Variables Specification

### Required Environment Variables

```bash
# IP Allowlist Configuration
ENABLE_IP_ALLOWLIST=false                    # Feature flag to enable/disable IP allowlist
IP_ALLOWLIST_CIDRS=10.0.0.0/8,**********/12,***********/16,127.0.0.1/32
IP_ALLOWLIST_LOG_VIOLATIONS=true            # Whether to log blocked requests
IP_ALLOWLIST_BLOCK_BY_DEFAULT=true          # Block if no rules match
IP_ALLOWLIST_INCLUDE_DEBUG=false            # Include debug info in error responses

# Sentry Configuration (Updated for PR3)
SENTRY_DSN=https://<EMAIL>/4509639010680912
ENABLE_SENTRY=true                           # Enable Sentry in production/staging
ENABLE_SENTRY_LOCAL=false                   # Enable Sentry in local development
SENTRY_TRACES_SAMPLE_RATE=0.01              # Production trace sampling rate (1%)
SENTRY_ENVIRONMENT=production                # Sentry environment identifier
```

### Environment Variable Validation

```typescript
/**
 * Environment variable validation schema
 */
export const IPAllowlistEnvSchema = {
  ENABLE_IP_ALLOWLIST: {
    type: 'boolean',
    default: false,
    description: 'Enable IP allowlist enforcement'
  },
  IP_ALLOWLIST_CIDRS: {
    type: 'string',
    default: '10.0.0.0/8,**********/12,***********/16,127.0.0.1/32',
    description: 'Comma-separated list of allowed CIDR ranges',
    validation: 'valid_cidr_list'
  },
  IP_ALLOWLIST_LOG_VIOLATIONS: {
    type: 'boolean',
    default: true,
    description: 'Log IP allowlist violations for monitoring'
  },
  IP_ALLOWLIST_BLOCK_BY_DEFAULT: {
    type: 'boolean',
    default: true,
    description: 'Block requests if no CIDR rules match'
  },
  IP_ALLOWLIST_INCLUDE_DEBUG: {
    type: 'boolean',
    default: false,
    description: 'Include debug information in error responses'
  }
};
```

## 🔒 Security Specifications

### 1. IP Address Validation

#### IPv4 Support
- **Format**: Standard dotted decimal notation (`***********`)
- **CIDR**: Support for `/0` to `/32` subnet masks
- **Validation**: RFC 5952 compliant parsing
- **Edge Cases**: Handle `0.0.0.0`, `127.0.0.1`, and broadcast addresses

#### IPv6 Support (Future)
- **Format**: Standard colon notation (`2001:db8::1`)
- **CIDR**: Support for `/0` to `/128` subnet masks
- **Validation**: RFC 4291 compliant parsing
- **Compression**: Handle zero compression and mixed notation

#### Header Processing Priority
1. `X-Forwarded-For` (first IP in comma-separated list)
2. `X-Real-IP` (single IP)
3. `CF-Connecting-IP` (Cloudflare)
4. `X-Client-IP` (generic proxy header)
5. Fallback to `127.0.0.1` for development

### 2. CIDR Range Validation

```typescript
/**
 * CIDR validation rules
 */
export const CIDRValidation = {
  // IPv4 CIDR patterns
  IPv4_PATTERN: /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})\/(\d{1,2})$/,
  
  // IPv6 CIDR patterns (future support)
  IPv6_PATTERN: /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\/(\d{1,3})$/,
  
  // Validation functions
  validateIPv4Octet: (octet: number) => octet >= 0 && octet <= 255,
  validateIPv4Prefix: (prefix: number) => prefix >= 0 && prefix <= 32,
  validateIPv6Prefix: (prefix: number) => prefix >= 0 && prefix <= 128,
};
```

### 3. Error Response Specification

```typescript
/**
 * Standardized error response for IP allowlist violations
 */
export interface IPAllowlistErrorResponse {
  error: 'IP_NOT_ALLOWED';
  message: string;
  statusCode: 403;
  timestamp: string;
  details?: {
    clientIP?: string;
    allowedRanges?: string[];
    requestId: string;
  };
  documentation: {
    allowlistInfo: string;
    contactSupport: string;
  };
}
```

### Example Error Response

```json
{
  "error": "IP_NOT_ALLOWED",
  "message": "Access denied: IP address not in allowlist",
  "statusCode": 403,
  "timestamp": "2025-07-13T11:11:00.000Z",
  "details": {
    "clientIP": "***********",
    "allowedRanges": ["10.0.0.0/8", "**********/12", "***********/16"],
    "requestId": "req_abc123def456"
  },
  "documentation": {
    "allowlistInfo": "https://docs.cashback-deals.com/security/ip-allowlist",
    "contactSupport": "https://support.cashback-deals.com/security-access"
  }
}
```

## 🚀 Performance Specifications

### 1. Performance Requirements

| Metric | Target | Measurement |
|--------|--------|-------------|
| **IP Validation Latency** | < 1ms | Time to validate single IP against CIDR list |
| **Memory Usage** | < 1MB | Total memory footprint for IP allowlist cache |
| **Throughput** | > 1000 req/sec | Requests processed with IP validation |
| **CPU Impact** | < 0.1% | Additional CPU usage per request |

### 2. Optimization Strategies

#### CIDR Matching Optimization
```typescript
/**
 * Optimized CIDR matching with pre-compiled masks
 */
export class OptimizedCIDRMatcher {
  private compiledRules: Array<{
    network: number;
    mask: number;
    originalCIDR: string;
  }>;
  
  constructor(cidrs: string[]) {
    this.compiledRules = cidrs.map(this.compileCIDR);
  }
  
  match(ip: string): { matched: boolean; rule?: string } {
    const ipNum = this.ipToNumber(ip);
    
    for (const rule of this.compiledRules) {
      if ((ipNum & rule.mask) === rule.network) {
        return { matched: true, rule: rule.originalCIDR };
      }
    }
    
    return { matched: false };
  }
}
```

#### Caching Strategy
- **CIDR Compilation**: Pre-compile CIDR rules at startup
- **Result Caching**: Cache IP validation results for 5 minutes
- **Configuration Caching**: Cache environment configuration for 1 hour
- **Memory Management**: LRU eviction with 1000 entry limit

## 🧪 Integration Patterns

### 1. API Route Integration

```typescript
// src/app/api/admin/config/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { applyIPAllowlist } from '@/lib/security/ip-allowlist';

export async function GET(request: NextRequest) {
  // Apply IP allowlist middleware
  const allowlistResponse = applyIPAllowlist(request);
  if (allowlistResponse) {
    return allowlistResponse; // Returns 403 if blocked
  }
  
  // Continue with admin functionality
  return NextResponse.json({ 
    message: "Admin configuration accessed",
    timestamp: new Date().toISOString()
  });
}
```

### 2. Custom Middleware Integration

```typescript
// middleware.ts (Next.js middleware)
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { applyIPAllowlist } from '@/lib/security/ip-allowlist';

export function middleware(request: NextRequest) {
  // Apply IP allowlist to admin routes
  if (request.nextUrl.pathname.startsWith('/api/admin/')) {
    const allowlistResponse = applyIPAllowlist(request);
    if (allowlistResponse) {
      return allowlistResponse;
    }
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: '/api/admin/:path*'
};
```

### 3. Feature Flag Integration

```typescript
/**
 * Feature flag integration for gradual rollout
 */
export function getIPAllowlistConfig(): IPAllowlistConfig {
  const baseConfig = {
    enabled: process.env.ENABLE_IP_ALLOWLIST === 'true',
    allowedCIDRs: parseCIDRList(process.env.IP_ALLOWLIST_CIDRS || ''),
    logViolations: process.env.IP_ALLOWLIST_LOG_VIOLATIONS !== 'false',
    blockByDefault: process.env.IP_ALLOWLIST_BLOCK_BY_DEFAULT !== 'false',
    includeDebugInfo: process.env.IP_ALLOWLIST_INCLUDE_DEBUG === 'true'
  };
  
  // Override for specific environments
  if (process.env.NODE_ENV === 'development') {
    baseConfig.enabled = false; // Disable in development by default
  }
  
  return baseConfig;
}
```

## 📊 Monitoring & Observability

### 1. Metrics Collection

```typescript
/**
 * Metrics interface for IP allowlist monitoring
 */
export interface IPAllowlistMetrics {
  // Request counters
  totalRequests: number;
  allowedRequests: number;
  blockedRequests: number;
  
  // Performance metrics
  avgValidationTimeMs: number;
  maxValidationTimeMs: number;
  
  // Error counters
  configurationErrors: number;
  validationErrors: number;
  
  // Top blocked IPs
  topBlockedIPs: Array<{ ip: string; count: number }>;
}
```

### 2. Logging Specification

```typescript
/**
 * Structured logging format for IP allowlist events
 */
export interface IPAllowlistLogEntry {
  timestamp: string;
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
  service: 'ip-allowlist';
  event: string;
  clientIP: string;
  path: string;
  userAgent?: string;
  result: 'allowed' | 'blocked' | 'error';
  matchedRule?: string;
  processingTimeMs: number;
  traceId: string;
}
```

### 3. Health Check Integration

```typescript
/**
 * Health check endpoint for IP allowlist functionality
 */
export async function healthCheck(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  details: {
    configLoaded: boolean;
    cidrRulesValid: boolean;
    performanceOk: boolean;
    lastError?: string;
  };
}> {
  // Implementation details in health check specification
}
```

---

**Technical Specifications Complete**: This document provides comprehensive technical details for implementing the IP allowlist middleware and Sentry cleanup functionality in PR3, following established patterns from PR1 and PR2.