# PR3: Sentry Cleanup & IP Allowlist - Deployment Guide

**Date:** July 13, 2025  
**Sprint:** Auth-Sprint Phase 1  
**Platform:** AWS Amplify  
**Deployment Strategy:** Feature-flagged rollout with monitoring  

## 🎯 Deployment Overview

Safe, phased deployment of Sentry cleanup and IP allowlist middleware to AWS Amplify with comprehensive rollback procedures and monitoring.

## 🏗️ Pre-Deployment Setup

### 1. AWS Amplify Environment Configuration

#### Environment Variables Setup
Navigate to **AWS Amplify Console** → **Your App** → **Environment variables**

```bash
# Sentry Configuration
SENTRY_DSN=https://<EMAIL>/4509639010680912
ENABLE_SENTRY=true
ENABLE_SENTRY_LOCAL=false
SENTRY_TRACES_SAMPLE_RATE=0.01              # 1% for production; teams may raise to 0.1 temporarily during bug-hunts
SENTRY_ENVIRONMENT=production

# IP Allowlist Configuration (Start with disabled)
ENABLE_IP_ALLOWLIST=false
IP_ALLOWLIST_CIDRS=10.0.0.0/8,**********/12,***********/16,127.0.0.1/32
IP_ALLOWLIST_LOG_VIOLATIONS=true
IP_ALLOWLIST_BLOCK_BY_DEFAULT=true
IP_ALLOWLIST_INCLUDE_DEBUG=false

# Feature Flags for Safe Rollout
NODE_ENV=production
```

#### Amplify Build Configuration
Ensure `amplify.yml` includes proper Node.js version with fallback:

```yaml
version: 1
frontend:
  phases:
    preBuild:
      commands:
        # Try Node 20.x with fallback to 18.x for AL2023 compatibility
        - export NVM_RC_VERSION="${NVM_RC_VERSION:-18}"
        - nvm install 20.10.0 || (echo "Node 20 install failed, falling back to Node 18" && nvm install $NVM_RC_VERSION && nvm use $NVM_RC_VERSION)
        - nvm use 20.10.0 || nvm use $NVM_RC_VERSION
        - npm ci
        # Run critical tests before build
        - npm test -- --testPathPattern="ip-allowlist.*lockout"
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
      - .next/cache/**/*
```

### 2. Pre-Deployment Validation

#### Local Testing Checklist
```bash
# 1. Run self-lockout prevention tests
npm test -- --testNamePattern="Self-Lockout Prevention"

# 2. Verify Sentry configuration
npm test -- --testPathPattern="sentry"

# 3. Test IP allowlist functionality
npm test -- --testPathPattern="ip-allowlist"

# 4. Build verification
npm run build

# 5. Start local server and verify endpoints
npm start
curl http://localhost:3000/api/sentry-example-api  # Should return 404
curl http://localhost:3000/sentry-example-page     # Should return 404
```

## 🚀 Deployment Phases

### Phase 1: Sentry Cleanup Deployment (Low Risk)

#### Step 1.1: Deploy with Monitoring Only
```bash
# Environment Variables (AWS Amplify Console)
ENABLE_IP_ALLOWLIST=false           # Keep IP allowlist disabled
ENABLE_SENTRY=true                  # Enable Sentry
SENTRY_TRACES_SAMPLE_RATE=0.01     # 1% sampling in production
```

#### Step 1.2: Deploy and Verify
1. **Trigger Deployment:** Push to main branch or deploy via Amplify Console
2. **Monitor Build:** Watch Amplify build logs for any errors
3. **Verify Endpoints:** 
   ```bash
   curl https://your-app.amplifyapp.com/api/sentry-example-api
   # Expected: 404 Not Found
   
   curl https://your-app.amplifyapp.com/sentry-example-page
   # Expected: 404 Not Found
   ```

#### Step 1.3: Sentry Health Check
```bash
# Test Sentry error reporting (use staging environment)
curl -X POST https://your-staging-app.amplifyapp.com/api/test-sentry-error
# Check Sentry dashboard for error capture
```

### Phase 2: IP Allowlist Deployment (Gradual Rollout)

#### Step 2.1: Enable IP Allowlist with Broad Ranges
```bash
# Update Environment Variables in AWS Amplify
ENABLE_IP_ALLOWLIST=true
IP_ALLOWLIST_CIDRS=10.0.0.0/8,**********/12,***********/16,127.0.0.1/32,0.0.0.0/0
# Note: 0.0.0.0/0 allows all IPs initially for testing
```

#### Step 2.2: Test IP Allowlist Functionality
```bash
# Test from office network (should be allowed)
curl https://your-app.amplifyapp.com/api/admin/test

# Test logging (check CloudWatch logs)
# Look for IP allowlist events in logs
```

**Important**: IP allowlist uses strict header precedence to prevent spoofing:
1. `X-Forwarded-For` (first IP in comma-separated list)
2. `X-Real-IP` (single IP)
3. `CF-Connecting-IP` (Cloudflare)
4. Fallback to `127.0.0.1` for development

#### Step 2.3: Tighten IP Restrictions
```bash
# Remove broad access, keep only company networks
ENABLE_IP_ALLOWLIST=true
IP_ALLOWLIST_CIDRS=10.0.0.0/8,**********/12,***********/16,127.0.0.1/32
# Remove 0.0.0.0/0 after testing
```

## 📊 Monitoring and Verification

### 1. AWS CloudWatch Monitoring

#### Log Groups to Monitor
- `/aws/amplify/your-app-name`
- Application logs for IP allowlist events
- Sentry error capture logs

#### Key Metrics to Watch
```bash
# IP Allowlist Violations
grep "IP_ALLOWLIST_VIOLATION" /aws/amplify/logs

# Sentry Error Capture
grep "Sentry.*captured" /aws/amplify/logs

# Build Failures
grep "Build failed" /aws/amplify/logs
```

### 2. Health Check Endpoints

#### Create Health Check Route
```typescript
// src/app/api/health/ip-allowlist/route.ts
export async function GET() {
  const config = getIPAllowlistConfig();
  const health = await healthCheckIPAllowlist();
  
  return NextResponse.json({
    status: health.status,
    timestamp: new Date().toISOString(),
    config: {
      enabled: config.enabled,
      cidrCount: config.allowedCIDRs.length
    }
  });
}
```

#### Monitor Health Endpoints
```bash
# Check IP allowlist health
curl https://your-app.amplifyapp.com/api/health/ip-allowlist

# Expected response:
{
  "status": "healthy",
  "timestamp": "2025-07-13T11:11:00.000Z",
  "config": {
    "enabled": true,
    "cidrCount": 4
  }
}
```

### 3. Automated Monitoring Setup

#### CloudWatch Alarms
```bash
# Create alarm for IP allowlist violations
aws cloudwatch put-metric-alarm \
  --alarm-name "IP-Allowlist-High-Violations" \
  --alarm-description "High number of IP allowlist violations" \
  --metric-name "IPAllowlistViolations" \
  --namespace "CashbackDeals/Security" \
  --statistic "Sum" \
  --period 300 \
  --threshold 50 \
  --comparison-operator "GreaterThanThreshold"
```

## 🔄 Rollback Procedures

### Emergency Rollback (< 1 minute)

#### Option 1: Feature Flag Rollback
```bash
# In AWS Amplify Console → Environment variables
ENABLE_IP_ALLOWLIST=false

# Redeploy (automatic with environment variable change)
```

#### Option 2: Broad IP Access (< 30 seconds)
```bash
# Temporarily allow all IPs while investigating
IP_ALLOWLIST_CIDRS=0.0.0.0/0

# This allows all traffic while maintaining logging
```

#### Option 3: Amplify Console Redeploy (≈20 seconds)
```bash
# In AWS Amplify Console → App → Hosting
# Click "Actions" → "Redeploy this version" on last successful build
# Fastest option for complete rollback to previous working state
```

### Full Rollback (< 5 minutes)

#### Git Revert and Redeploy
```bash
# Revert to previous commit
git revert HEAD
git push origin main

# AWS Amplify will automatically trigger rebuild
# Monitor deployment in Amplify Console
```

### Rollback Verification
```bash
# Verify rollback success
curl https://your-app.amplifyapp.com/api/health/ip-allowlist
# Should show disabled status or previous configuration

# Test access from external IP
curl -H "X-Forwarded-For: ***********" https://your-app.amplifyapp.com/api/admin/test
# Should succeed after rollback
```

## ✅ Post-Deployment Verification

### 1. Functional Verification Checklist

#### Sentry Cleanup Verification
- [ ] `/api/sentry-example-api` returns 404
- [ ] `/sentry-example-page` returns 404  
- [ ] Sentry error reporting works normally
- [ ] Sentry dashboard shows reduced trace volume (1% sampling)
- [ ] No increase in application error rates

#### IP Allowlist Verification
- [ ] Office IPs can access protected endpoints
- [ ] External IPs are blocked with 403 responses
- [ ] Feature flag disable/enable works correctly
- [ ] CloudWatch logs show IP allowlist events
- [ ] Health check endpoint responds correctly

### 2. Performance Verification
```bash
# Test response times (should be < 1ms impact)
curl -w "@curl-format.txt" https://your-app.amplifyapp.com/api/admin/test

# Monitor memory usage in CloudWatch
# Verify no memory leaks from IP allowlist cache
```

### 3. Security Verification
```bash
# Test IP spoofing protection
curl -H "X-Forwarded-For: ********" \
     -H "X-Real-IP: ***********" \
     https://your-app.amplifyapp.com/api/admin/test
# Should use X-Forwarded-For (first header)

# Test IPv6 support
curl -H "X-Forwarded-For: 2001:db8::1" \
     https://your-app.amplifyapp.com/api/admin/test
# Should handle IPv6 correctly
```

## 🚨 Troubleshooting Guide

### Common Issues and Solutions

#### Issue: Self-Lockout from Office Network
```bash
# Emergency fix: Allow all IPs temporarily
IP_ALLOWLIST_CIDRS=0.0.0.0/0

# Then investigate and fix office IP ranges
# Check actual office IP: curl ifconfig.me
```

#### Issue: Sentry Not Capturing Errors
```bash
# Check environment variables
echo $SENTRY_DSN
echo $ENABLE_SENTRY

# Verify Sentry configuration in logs
grep "Sentry" /aws/amplify/logs
```

#### Issue: Build Failures
```bash
# Check for CIDR validation errors
npm test -- --testNamePattern="CIDR.*validation"

# Verify environment variables are set correctly
```

### Support Contacts
- **DevOps Team:** <EMAIL>
- **Security Team:** <EMAIL>  
- **AWS Support:** (if Amplify issues)

---

**Deployment Guide Complete**: This guide provides comprehensive, AWS Amplify-specific deployment procedures with safety measures and monitoring for PR3 implementation.
