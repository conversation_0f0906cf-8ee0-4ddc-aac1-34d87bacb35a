<testsuites id="" name="" tests="3" failures="3" skipped="0" errors="0" time="45.35657500000001">
<testsuite name="auth-hmac.spec.ts" timestamp="2025-07-13T02:14:50.028Z" hostname="chromium" tests="1" failures="1" skipped="0" time="31.156" errors="0">
<testcase name="HMAC Authentication E2E Tests › Search API Authentication › should accept valid HMAC authentication" classname="auth-hmac.spec.ts" time="31.156">
<failure message="auth-hmac.spec.ts:75:9 should accept valid HMAC authentication" type="FAILURE">
<![CDATA[  [chromium] › auth-hmac.spec.ts:75:9 › HMAC Authentication E2E Tests › Search API Authentication › should accept valid HMAC authentication 

    Test timeout of 30000ms exceeded while running "beforeEach" hook.

      55 |
      56 | test.describe('HMAC Authentication E2E Tests', () => {
    > 57 |   test.beforeEach(async ({ page }) => {
         |        ^
      58 |     // Set up any necessary test environment
      59 |     await page.goto(BASE_URL)
      60 |   })
        at /Users/<USER>/cashback-deals-v2/tests/e2e/auth-hmac.spec.ts:57:8

    Error: page.goto: Test timeout of 30000ms exceeded.
    Call log:
      - navigating to "http://localhost:3000/", waiting until "load"


      57 |   test.beforeEach(async ({ page }) => {
      58 |     // Set up any necessary test environment
    > 59 |     await page.goto(BASE_URL)
         |                ^
      60 |   })
      61 |
      62 |   test.describe('Search API Authentication', () => {
        at /Users/<USER>/cashback-deals-v2/tests/e2e/auth-hmac.spec.ts:59:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../../test-results/auth-hmac-HMAC-Authenticat-d5e87-t-valid-HMAC-authentication-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ../../test-results/auth-hmac-HMAC-Authenticat-d5e87-t-valid-HMAC-authentication-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results/auth-hmac-HMAC-Authenticat-d5e87-t-valid-HMAC-authentication-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|auth-hmac-HMAC-Authenticat-d5e87-t-valid-HMAC-authentication-chromium/test-failed-1.png]]

[[ATTACHMENT|auth-hmac-HMAC-Authenticat-d5e87-t-valid-HMAC-authentication-chromium/video.webm]]

[[ATTACHMENT|auth-hmac-HMAC-Authenticat-d5e87-t-valid-HMAC-authentication-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="auth-hmac.spec.ts" timestamp="2025-07-13T02:14:50.028Z" hostname="firefox" tests="1" failures="1" skipped="0" time="30.277" errors="0">
<testcase name="HMAC Authentication E2E Tests › Search API Authentication › should accept valid HMAC authentication" classname="auth-hmac.spec.ts" time="30.277">
<failure message="auth-hmac.spec.ts:75:9 should accept valid HMAC authentication" type="FAILURE">
<![CDATA[  [firefox] › auth-hmac.spec.ts:75:9 › HMAC Authentication E2E Tests › Search API Authentication › should accept valid HMAC authentication 

    Test timeout of 30000ms exceeded.

    Error: apiRequestContext.get: Request context disposed.
    Call log:
      - → GET http://localhost:3000/api/search?q=laptop
        - user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0.2) Gecko/20100101 Firefox/140.0.2
        - accept: */*
        - accept-encoding: gzip,deflate,br
        - X-Signature: sha256=762800168d460c535409784d1646414ec2e109347f59672a957295a3e9224373
        - X-Timestamp: 1752372897
        - X-Partner-ID: test-partner
        - Content-Type: application/json
        - X-Version: 1.0


      77 |       const headers = createHMACHeaders('GET', path, '', { includeVersion: true })
      78 |       
    > 79 |       const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
         |                                      ^
      80 |       
      81 |       expect(response.status()).toBe(200)
      82 |       
        at /Users/<USER>/cashback-deals-v2/tests/e2e/auth-hmac.spec.ts:79:38

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../../test-results/auth-hmac-HMAC-Authenticat-d5e87-t-valid-HMAC-authentication-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ../../test-results/auth-hmac-HMAC-Authenticat-d5e87-t-valid-HMAC-authentication-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results/auth-hmac-HMAC-Authenticat-d5e87-t-valid-HMAC-authentication-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|auth-hmac-HMAC-Authenticat-d5e87-t-valid-HMAC-authentication-firefox/test-failed-1.png]]

[[ATTACHMENT|auth-hmac-HMAC-Authenticat-d5e87-t-valid-HMAC-authentication-firefox/video.webm]]

[[ATTACHMENT|auth-hmac-HMAC-Authenticat-d5e87-t-valid-HMAC-authentication-firefox/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="auth-hmac.spec.ts" timestamp="2025-07-13T02:14:50.028Z" hostname="webkit" tests="1" failures="1" skipped="0" time="35.129" errors="0">
<testcase name="HMAC Authentication E2E Tests › Search API Authentication › should accept valid HMAC authentication" classname="auth-hmac.spec.ts" time="35.129">
<failure message="auth-hmac.spec.ts:75:9 should accept valid HMAC authentication" type="FAILURE">
<![CDATA[  [webkit] › auth-hmac.spec.ts:75:9 › HMAC Authentication E2E Tests › Search API Authentication › should accept valid HMAC authentication 

    Test timeout of 30000ms exceeded while running "beforeEach" hook.

      55 |
      56 | test.describe('HMAC Authentication E2E Tests', () => {
    > 57 |   test.beforeEach(async ({ page }) => {
         |        ^
      58 |     // Set up any necessary test environment
      59 |     await page.goto(BASE_URL)
      60 |   })
        at /Users/<USER>/cashback-deals-v2/tests/e2e/auth-hmac.spec.ts:57:8

    TimeoutError: page.goto: Timeout 30000ms exceeded.
    Call log:
      - navigating to "http://localhost:3000/", waiting until "load"


      57 |   test.beforeEach(async ({ page }) => {
      58 |     // Set up any necessary test environment
    > 59 |     await page.goto(BASE_URL)
         |                ^
      60 |   })
      61 |
      62 |   test.describe('Search API Authentication', () => {
        at /Users/<USER>/cashback-deals-v2/tests/e2e/auth-hmac.spec.ts:59:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../../test-results/auth-hmac-HMAC-Authenticat-d5e87-t-valid-HMAC-authentication-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ../../test-results/auth-hmac-HMAC-Authenticat-d5e87-t-valid-HMAC-authentication-webkit/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|auth-hmac-HMAC-Authenticat-d5e87-t-valid-HMAC-authentication-webkit/test-failed-1.png]]

[[ATTACHMENT|auth-hmac-HMAC-Authenticat-d5e87-t-valid-HMAC-authentication-webkit/video.webm]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>